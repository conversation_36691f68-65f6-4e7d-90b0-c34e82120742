import {
  parseHeaders,
  parsePath,
  parseQueryString,
} from "../../shared/parsers";
import { message, json } from "../../shared/response";
import { readCustomerByStatus } from "../../model/dynamo";
import { DatabaseEntity } from "../../entities/database-entity";
import { CustomerEntity } from "../../entities/customer-entity";
import { makeDynamoDB } from "../../shared/services/dynamo-service";
async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event) => {
  console.log("event", event);
  const url = parsePath(event);
  const head = parseHeaders(event);
  const { status, isActive } = parseQueryString(event);
  const { form } = url;
  const { dynamodb } = head;
  const clientDynamo = makeDynamoDB();
  const dbEntity = new DatabaseEntity(clientDynamo);
  const customerEntity = new CustomerEntity(dbEntity);

  // Usa o header dynamodb se fornecido, senão usa a variável de ambiente
  const tableName = dynamodb || `${process.env.FINOPS_STAGE || process.env.STAGE || 'dev'}-customers`;

  if (!form)
    return await sendDataToUser(400, "error", "Valid form not provided");
  if (!status && !isActive)
    return await sendDataToUser(400, "error", "No valid query string provided");

  try {
    const params = {
      status: async () =>
        await dbEntity.readCustomersByStatus(
          customerEntity.generateReadCustomersByStatusInput({
            status,
            dynamodb: tableName,
          })
        ),
      hasActiveContracts: async () =>
        await dbEntity.readCustomersByStatus(
          customerEntity.generateReadCustomersByStatusActiveInput({
            status,
            dynamodb: tableName,
            isActive,
          })
        ),
      billing: async () =>
        await dbEntity.readCustomersByStatus(
          customerEntity.generateReadCustomersByStatusBillingInput({
            dynamodb: tableName,
          })
        ),
    };

    const readObjDynamo = await params[form]();
    return await sendDataToUser(200, "success", readObjDynamo);
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};