"use strict";

export const STATUS_CODE = {
  SUCCESS: 200,
  BAD_REQUEST: 400,
  NOT_FOUND: 404,
  CONFLICT: 409,
  ERROR: 500,
};

export async function message(status, data) {
  return {
    status,
    data,
  };
}

// Modificar a lógica para usar a origem da requisição
const getOrigin = (event, allowedOrigins) => {
  // Se temos * nas origens permitidas, verificamos a origem da requisição
  if (allowedOrigins.includes('*')) {
    // Se temos o evento e headers, usamos a origem da requisição se estiver na lista
    if (event && event.headers && event.headers.origin) {
      return event.headers.origin;
    }
    // Caso contrário, usamos a primeira origem específica ou *
    return allowedOrigins.find(o => o !== '*') || '*';
  }
  
  // Se não temos *, verificamos se a origem da requisição está na lista
  if (event && event.headers && event.headers.origin) {
    return allowedOrigins.includes(event.headers.origin) ? 
      event.headers.origin : allowedOrigins[0];
  }
  
  // <PERSON><PERSON>o contr<PERSON>, usamos a primeira origem
  return allowedOrigins[0];
};

export async function json(body = {}, status = 200, additionalHeaders = {}, event = null) {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ?
    process.env.ALLOWED_ORIGINS.split(',') : ['*'];

  const origin = getOrigin(event, allowedOrigins);
  
  const headers = {
    "Access-Control-Allow-Origin": origin,
    "Access-Control-Allow-Credentials": "true",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, DynamoDB",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    ...additionalHeaders
  };

  return {
    statusCode: status,
    headers,
    body: body != null ? JSON.stringify(body.stack ? body.stack : body) : "",
  };
}

export const responseWithError = (message, additionalHeaders = {}) => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ?
    process.env.ALLOWED_ORIGINS.split(',') : ['*'];

  const origin = allowedOrigins.includes('*') ? '*' : allowedOrigins[0];

  return {
    statusCode: STATUS_CODE.ERROR,
    headers: {
      "Access-Control-Allow-Origin": origin,
      "Access-Control-Allow-Credentials": "true",
      ...additionalHeaders
    },
    body: JSON.stringify({
      message,
    }),
  };
};

export const responseWithBadRequest = (message, additionalHeaders = {}) => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ?
    process.env.ALLOWED_ORIGINS.split(',') : ['*'];

  const origin = allowedOrigins.includes('*') ? '*' : allowedOrigins[0];

  return {
    statusCode: STATUS_CODE.BAD_REQUEST,
    headers: {
      "Access-Control-Allow-Origin": origin,
      "Access-Control-Allow-Credentials": "true",
      ...additionalHeaders
    },
    body: JSON.stringify({
      message,
    }),
  };
};

export const responseWithNotFound = (message) => {
  return {
    statusCode: STATUS_CODE.NOT_FOUND,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    body: JSON.stringify({
      message,
    }),
  };
};

export const responseWithConflict = (message) => {
  return {
    statusCode: STATUS_CODE.CONFLICT,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    body: JSON.stringify({
      message,
    }),
  };
};

export const responseWithSuccess = (data, message = "", additionalHeaders = {}) => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ?
    process.env.ALLOWED_ORIGINS.split(',') : ['*'];

  const origin = allowedOrigins.includes('*') ? '*' : allowedOrigins[0];

  return {
    statusCode: STATUS_CODE.SUCCESS,
    headers: {
      "Access-Control-Allow-Origin": origin,
      "Access-Control-Allow-Credentials": "true",
      ...additionalHeaders
    },
    body: JSON.stringify(data || message),
  };
};

/**
 * Resposta de sucesso com cookies
 * @param {*} data - Dados da resposta
 * @param {Array} cookies - Array de strings de cookies
 * @param {string} message - Mensagem opcional
 * @returns {Object} Resposta Lambda com cookies
 */
export const responseWithCookies = (data, cookies = [], message = "") => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ?
    process.env.ALLOWED_ORIGINS.split(',') : ['*'];

  const origin = allowedOrigins.includes('*') ? '*' : allowedOrigins[0];

  const headers = {
    "Access-Control-Allow-Origin": origin,
    "Access-Control-Allow-Credentials": "true",
  };

  if (cookies.length > 0) {
    headers['Set-Cookie'] = cookies.length === 1 ? cookies[0] : cookies;
  }

  return {
    statusCode: STATUS_CODE.SUCCESS,
    headers,
    body: JSON.stringify(data || message),
  };
};

/**
 * Resposta para logout com cookies de limpeza
 * @param {Array} deleteCookies - Array de cookies para deletar
 * @param {string} message - Mensagem de logout
 * @returns {Object} Resposta Lambda
 */
export const responseWithLogout = (deleteCookies = [], message = "Logout realizado com sucesso") => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ?
    process.env.ALLOWED_ORIGINS.split(',') : ['*'];

  const origin = allowedOrigins.includes('*') ? '*' : allowedOrigins[0];

  const headers = {
    "Access-Control-Allow-Origin": origin,
    "Access-Control-Allow-Credentials": "true",
  };

  if (deleteCookies.length > 0) {
    headers['Set-Cookie'] = deleteCookies.length === 1 ? deleteCookies[0] : deleteCookies;
  }

  return {
    statusCode: STATUS_CODE.SUCCESS,
    headers,
    body: JSON.stringify({ message }),
  };
};
