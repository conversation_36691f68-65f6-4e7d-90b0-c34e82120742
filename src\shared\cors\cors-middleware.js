/**
 * Middleware CORS para Lambda com suporte completo a cookies HttpOnly
 * Implementa todas as especificações CORS necessárias para cookies seguros
 */

/**
 * Configuração CORS baseada no ambiente
 */
const getCorsConfig = () => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ? 
    process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim()) : 
    ['http://localhost:3000', 'http://localhost:8080'];
  
  // Em produção, usar apenas origens específicas
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    allowedOrigins: isProduction ? 
      allowedOrigins.filter(origin => !origin.includes('localhost')) : 
      allowedOrigins,
    allowedHeaders: [
      'Content-Type',
      'X-Requested-With',
      'Authorization',
      'Cookie',
      'Set-Cookie',
      'X-Amz-Date',
      'X-Api-Key',
      'X-Amz-Security-Token',
      'X-Amz-User-Agent',
      'X-Forwarded-For',
      'X-Forwarded-Proto',
      'DynamoDB'
    ],
    allowedMethods: [
      'GET',
      'POST',
      'PUT',
      'DELETE',
      'OPTIONS',
      'HEAD'
    ],
    credentials: true,
    maxAge: 86400, // 24 horas
    optionsSuccessStatus: 200
  };
};

/**
 * Verifica se a origem é permitida
 * @param {string} origin - Origem da requisição
 * @param {Array} allowedOrigins - Origens permitidas
 * @returns {boolean} True se permitida
 */
const isOriginAllowed = (origin, allowedOrigins) => {
  if (!origin) return false;

  // Debug temporário
  console.log(`🔍 CORS Debug: Verificando origem '${origin}' contra:`, allowedOrigins);

  // Correspondência exata
  if (allowedOrigins.includes(origin)) {
    console.log(`✅ CORS: Origem '${origin}' permitida (correspondência exata)`);
    return true;
  }

  // Verifica padrões com wildcard e subdomínios
  try {
    const originUrl = new URL(origin);
    const originHost = originUrl.hostname;
    const originProtocol = originUrl.protocol;

    // Verifica padrões com wildcard nas origens permitidas
    const matchesWildcard = allowedOrigins.some(allowed => {
      if (allowed.includes('*')) {
        console.log(`🔍 CORS: Testando padrão wildcard '${allowed}' contra '${origin}'`);

        // Extrai protocolo e padrão
        let allowedProtocol = 'https:';
        let allowedPattern = allowed;

        if (allowed.startsWith('http://') || allowed.startsWith('https://')) {
          const allowedUrl = new URL(allowed.replace('*', 'wildcard'));
          allowedProtocol = allowedUrl.protocol;
          allowedPattern = allowed.replace(/^https?:\/\//, '');
        }

        // Verifica se o protocolo corresponde
        if (originProtocol !== allowedProtocol) {
          return false;
        }

        if (allowedPattern.startsWith('*.')) {
          // Padrão *.domain.com
          const domain = allowedPattern.substring(2); // Remove "*."
          const matches = originHost.endsWith('.' + domain) || originHost === domain;
          console.log(`🔍 CORS: Padrão '*.${domain}' vs host '${originHost}': ${matches}`);
          return matches;
        }
      }
      return false;
    });

    if (matchesWildcard) {
      console.log(`✅ CORS: Origem '${origin}' permitida (padrão wildcard)`);
      return true;
    }

    // Verifica se é um subdomínio específico de dsm.darede.com.br (fallback adicional)
    if (originHost.endsWith('.dsm.darede.com.br') || originHost === 'dsm.darede.com.br') {
      console.log(`✅ CORS: Origem '${origin}' permitida (subdomínio de dsm.darede.com.br - fallback)`);
      return true;
    }
  } catch (e) {
    console.log(`⚠️ CORS: Erro ao processar URL '${origin}':`, e.message);
    // Se não conseguir fazer parse da URL, continua com as outras verificações
  }

  // Verifica padrões localhost (para desenvolvimento)
  if (process.env.NODE_ENV !== 'production') {
    const isLocalhost = allowedOrigins.some(allowed => {
      if (allowed.includes('localhost')) {
        try {
          const allowedHost = new URL(allowed).host;
          const originHost = new URL(origin).host;
          return allowedHost === originHost;
        } catch (e) {
          return false;
        }
      }
      return false;
    });

    if (isLocalhost) {
      console.log(`✅ CORS: Origem '${origin}' permitida (localhost em desenvolvimento)`);
      return true;
    }
  }

  console.log(`❌ CORS: Origem '${origin}' REJEITADA`);
  return false;
};

/**
 * Cria headers CORS para a resposta
 * @param {Object} event - Evento Lambda
 * @param {Object} config - Configuração CORS
 * @returns {Object} Headers CORS
 */
const createCorsHeaders = (event, config) => {
  const origin = event.headers?.Origin || event.headers?.origin;
  const requestMethod = event.headers?.['Access-Control-Request-Method'];
  const requestHeaders = event.headers?.['Access-Control-Request-Headers'];
  
  const headers = {
    'Vary': 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers'
  };
  
  if (isOriginAllowed(origin, config.allowedOrigins)) {
    headers['Access-Control-Allow-Origin'] = origin;
    headers['Access-Control-Allow-Methods'] = config.allowedMethods.join(', ');
    headers['Access-Control-Allow-Headers'] = config.allowedHeaders.join(', ');
    headers['Access-Control-Max-Age'] = config.maxAge.toString();

    if (config.credentials) {
      headers['Access-Control-Allow-Credentials'] = 'true';
    }

    console.log(`CORS: Headers definidos para origem permitida: ${origin}`);
    console.log('CORS Headers:', headers);
  } else {
    // Em desenvolvimento, adiciona header de erro
    if (process.env.NODE_ENV !== 'production') {
      headers['X-CORS-Error'] = 'Origin not allowed';
    }

    // Não define nenhum header CORS para origens não permitidas
    // Isso fará com que o browser rejeite a requisição
  }
  
  // Headers específicos para preflight (apenas se origem for permitida)
  if (event.httpMethod === 'OPTIONS' && isOriginAllowed(origin, config.allowedOrigins)) {
    if (requestMethod && config.allowedMethods.includes(requestMethod)) {
      // Já definido acima se origem for permitida
    }

    if (requestHeaders) {
      const requestedHeaders = requestHeaders.split(',').map(h => h.trim());
      const allowedRequestHeaders = requestedHeaders.filter(h =>
        config.allowedHeaders.some(allowed =>
          allowed.toLowerCase() === h.toLowerCase()
        )
      );

      // Headers já definidos acima se origem for permitida
    }
  }
  
  return headers;
};

/**
 * Middleware CORS principal
 * @param {Function} handler - Handler da função Lambda
 * @param {Object} options - Opções de configuração CORS
 * @returns {Function} Handler com CORS aplicado
 */
const withCors = (handler, options = {}) => {
  const config = {
    ...getCorsConfig(),
    ...options
  };
  
  return async (event, context) => {
    try {
      console.log(`🌐 CORS: Processando ${event.httpMethod} ${event.path} de ${event.headers?.Origin || 'origem desconhecida'}`);
      const corsHeaders = createCorsHeaders(event, config);
      console.log('🌐 CORS Headers criados:', corsHeaders);
      
      if (event.httpMethod === 'OPTIONS') {
        // console.log('CORS: Respondendo a requisição preflight OPTIONS');

        const origin = event.headers?.Origin || event.headers?.origin;
        console.log(`🌐 CORS: Verificando origem para preflight: ${origin}`);
        console.log(`🌐 CORS: Origens permitidas:`, config.allowedOrigins);

        if (!isOriginAllowed(origin, config.allowedOrigins)) {
          console.log(`🚫 CORS: Preflight rejeitado para origem não permitida: ${origin}`);
          return {
            statusCode: 403,
            headers: {
              'Content-Type': 'application/json',
              'X-CORS-Error': 'Origin not allowed'
            },
            body: JSON.stringify({
              error: 'CORS policy violation',
              message: 'Origin not allowed',
              origin: origin
            })
          };
        }

        return {
          statusCode: config.optionsSuccessStatus,
          headers: corsHeaders,
          body: ''
        };
      }
      
      // Verifica origem para requisições normais também
      const origin = event.headers?.Origin || event.headers?.origin;
      console.log(`🌐 CORS: Verificando origem para requisição normal: ${origin}`);

      if (origin && !isOriginAllowed(origin, config.allowedOrigins)) {
        console.log(`🚫 CORS: Requisição rejeitada para origem não permitida: ${origin}`);
        return {
          statusCode: 403,
          headers: {
            'Content-Type': 'application/json',
            'X-CORS-Error': 'Origin not allowed'
          },
          body: JSON.stringify({
            error: 'CORS policy violation',
            message: 'Origin not allowed',
            origin: origin
          })
        };
      }

      const result = await handler(event, context);

      const responseHeaders = {
        ...corsHeaders,
        ...(result.headers || {})
      };
      
      if (!responseHeaders['Content-Type']) {
        responseHeaders['Content-Type'] = 'application/json';
      }
      
      // console.log(`CORS: Headers aplicados para ${event.headers?.Origin || 'origem desconhecida'}`);
      
      return {
        ...result,
        headers: responseHeaders
      };
      
    } catch (error) {
      console.error('CORS: Erro no middleware:', error);
      
      // Retorna erro com headers CORS
      const corsHeaders = createCorsHeaders(event, config);
      
      return {
        statusCode: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: 'Erro interno do servidor',
          message: error.message
        })
      };
    }
  };
};

/**
 * Middleware CORS específico para endpoints de autenticação
 * Configuração otimizada para cookies HttpOnly
 */
const withAuthCors = (handler) => {
  return withCors(handler, {
    credentials: true,
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'Cookie',
      'Set-Cookie',
      'X-Requested-With'
    ],
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    maxAge: 3600 // 1 hora para endpoints de auth
  });
};

/**
 * Middleware CORS para endpoints públicos
 * Configuração mais restritiva
 */
const withPublicCors = (handler) => {
  return withCors(handler, {
    credentials: false,
    allowedHeaders: [
      'Content-Type',
      'X-Requested-With'
    ],
    allowedMethods: ['GET', 'OPTIONS'],
    maxAge: 86400 // 24 horas para endpoints públicos
  });
};

/**
 * Utilitário para validar configuração CORS
 */
const validateCorsConfig = () => {
  const config = getCorsConfig();
  
  // console.log('CORS: Configuração carregada:', {
  //   allowedOrigins: config.allowedOrigins,
  //   credentials: config.credentials,
  //   environment: process.env.NODE_ENV
  // });
  
  // Validações
  if (config.allowedOrigins.includes('*') && config.credentials) {
    console.warn('CORS: AVISO - Não é possível usar credentials: true com origin: "*"');
  }
  
  if (process.env.NODE_ENV === 'production' && 
      config.allowedOrigins.some(origin => origin.includes('localhost'))) {
    console.warn('CORS: AVISO - Origens localhost detectadas em produção');
  }
  
  return config;
};

module.exports = {
  withCors,
  withAuthCors,
  withPublicCors,
  createCorsHeaders,
  getCorsConfig,
  validateCorsConfig,
  isOriginAllowed
};
