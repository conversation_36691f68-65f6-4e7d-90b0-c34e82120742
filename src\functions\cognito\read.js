const { CognitoIdentityServiceProvider } = require('aws-sdk');
const { responseWithSuccess, responseWithError } = require('../../shared/response-cjs');

const cognito = new CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION_LOCATION
})

async function sendDataToUser (status, statusString, data) {
  if (status === 200) {
    return responseWithSuccess(data, statusString);
  } else {
    return responseWithError(statusString, data);
  }
}

exports.handler = async (event, context) => {
  try {
    console.log('🔍 Cognito read endpoint called');
    console.log('USER_POOL_ID:', process.env.USER_POOL_ID);

    const read = await cognito.listUsers({
      UserPoolId: process.env.USER_POOL_ID
    }).promise()

    console.log('✅ Cognito listUsers successful, users found:', read.Users.length);

    while (read.PaginationToken) {
      const { Users, PaginationToken } = await cognito.listUsers({
        UserPoolId: process.env.USER_POOL_ID,
        PaginationToken: read.PaginationToken
      }).promise()

      read.Users.push(...Users)

      read.PaginationToken = PaginationToken
    }

    const response = read.Users.map(user => {
      const obj = {}

      user.Attributes.map(attribute => {
        const refactorName = {
          email: () => obj.email = attribute.Value,
          role: () => obj.permission = attribute.Value,
          hml_role: () => obj.hml_permission = attribute.Value,
          dev_role: () => obj.dev_permission = attribute.Value
        }

        try {
          refactorName[attribute?.Name?.replace('custom:', '')]()
        } catch (err) {
          // console.log(err)
        }

        if (user.Username.includes('\\')) obj.user = user.Username.split('\\')[1]
        else obj.user = user.Username
        obj.status = user.Enabled

        return attribute
      })

      return obj
    })

    const filteredResponse = response.filter(e => e?.user !== undefined);
    console.log('📊 Resposta processada:', {
      totalUsers: read.Users.length,
      filteredUsers: filteredResponse.length
    });

    console.log('🚀 Retornando resposta de sucesso...');
    return await sendDataToUser(200, 'success', filteredResponse);
  } catch (error) {
    console.error('❌ Erro no cognito read:', error);
    return await sendDataToUser(500, 'error', error.message || 'Erro interno');
  }
};
