const { SecretsManager } = require('aws-sdk');
const { parseBody } = require('../../shared/parsers-cjs');
const { responseWithSuccess, responseWithError } = require('../../shared/response-cjs');

const cli = new SecretsManager({
  region: process.env.AWS_REGION_LOCATION
})

async function sendDataToUser (status, statusString, data) {
  if (status === 200) {
    return responseWithSuccess(data, statusString);
  } else {
    return responseWithError(statusString, data);
  }
}

const mfaHandler = async (event) => {
  console.log('🔐 MFA endpoint called (POST)');

  const body = parseBody(event);
  console.log('Body recebido:', body);

  try {
    const all = await cli.getSecretValue({
      SecretId: process.env.AWS_SECRET_GOOGLE_AUTH_TOKEN
    }).promise()

    const params = JSON.parse(all.SecretString)

    if (body.only_read == 0) {
      params[body.user] = body.secret

      const secret = await cli.putSecretValue({
        SecretId: process.env.AWS_SECRET_GOOGLE_AUTH_TOKEN,
        SecretString: JSON.stringify(params)
      }).promise()

      console.log(secret)
    } else if (body.only_read == 2) {
      delete params[body.user]

      const secret = await cli.putSecretValue({
        SecretId: process.env.AWS_SECRET_GOOGLE_AUTH_TOKEN,
        SecretString: JSON.stringify(params)
      }).promise()

      console.log(secret)
    }

    return await sendDataToUser(200, 'success', params)
  } catch (error) {
    return await sendDataToUser(200, 'success', error)
  }
}

exports.handler = async (event) => {
  return mfaHandler(event);
};
