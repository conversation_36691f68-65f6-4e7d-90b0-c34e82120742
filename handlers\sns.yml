sns-publish:
  handler: src/functions/sns/publish.handler
  name: ${self:custom.dotenv.STAGE}-sns-publish${self:custom.dotenv.VERSION}
  description: Função para publicar uma mensagem no SNS do switch role
  memorySize: 128
  events:
    - http:
        path: /sns/publish
        method: post
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}


