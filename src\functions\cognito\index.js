/**
 * Endpoint /cognito - Redireciona para endpoints específicos do Cognito
 * Compatibilidade com frontend que chama POST /cognito
 */

const { parseBody, parseQueryString } = require('../../shared/parsers-cjs');
const { responseWithSuccess, responseWithError, responseWithBadRequest } = require('../../shared/response-cjs');
const { withAuthCors } = require('../../shared/cors/cors-middleware');

/**
 * Handler principal para endpoint /cognito
 */
const cognitoHandler = async (event) => {
  try {
    console.log('🔍 Cognito endpoint called');
    console.log('Method:', event.httpMethod);
    console.log('Origin header:', event.headers?.Origin || event.headers?.origin);
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const method = event.httpMethod;
    
    let response;
    switch (method) {
      case 'GET':
        response = await handleGet(event);
        break;
      case 'POST':
        response = await handlePost(event);
        break;
      default:
        console.log('❌ Método não suportado:', method);
        response = responseWithBadRequest(`Método ${method} não suportado`);
    }

    console.log('🚀 Retornando resposta do cognito handler:', {
      statusCode: response.statusCode,
      hasHeaders: !!response.headers,
      hasMultiValueHeaders: !!response.multiValueHeaders
    });

    return response;
    
  } catch (error) {
    console.error('❌ Erro no endpoint cognito:', error);
    return responseWithError('Erro interno no processamento Cognito');
  }
};

/**
 * Trata requisições GET /cognito
 */
async function handleGet(event) {
  console.log('📄 Processando GET /cognito');
  
  const queryParams = parseQueryString(event);
  console.log('Query params:', queryParams);
  
  // Redireciona para /cognito/read
  return responseWithSuccess('Redirecionamento para Cognito Read', {
    message: 'Use o endpoint específico /cognito/read para listar usuários',
    availableEndpoints: {
      read: '/cognito/read',
      token: '/cognito/token',
      accessToken: '/cognito/access-token'
    },
    queryParams: queryParams
  });
}

/**
 * Trata requisições POST /cognito
 */
async function handlePost(event) {
  console.log('📝 Processando POST /cognito');
  
  const body = parseBody(event);
  console.log('Body recebido:', body);
  
  // Verifica se é uma tentativa de autenticação
  if (body && (body.token || body.code || body.user)) {
    console.log('🔐 Detectada tentativa de autenticação');
    
    return responseWithSuccess('Redirecionamento para autenticação', {
      message: 'Para autenticação, use o endpoint /auth/set-token',
      receivedData: body,
      correctEndpoint: '/auth/set-token',
      instructions: {
        method: 'POST',
        body: { token: 'seu_token_cognito_aqui' },
        description: 'Envie o token Cognito para obter cookies HttpOnly'
      }
    });
  }
  
  // Verifica se é uma tentativa de listar usuários
  if (body && (body.action === 'list' || body.listUsers)) {
    console.log('📋 Detectada tentativa de listar usuários');
    
    return responseWithSuccess('Redirecionamento para listagem', {
      message: 'Para listar usuários, use o endpoint /cognito/read',
      correctEndpoint: '/cognito/read',
      instructions: {
        method: 'GET',
        description: 'Retorna lista de usuários do Cognito'
      }
    });
  }
  
  // Caso genérico
  return responseWithSuccess('Endpoint Cognito', {
    message: 'Endpoint /cognito recebeu requisição POST',
    receivedData: body,
    availableEndpoints: {
      authentication: {
        endpoint: '/auth/set-token',
        method: 'POST',
        description: 'Autenticação com token Cognito'
      },
      listUsers: {
        endpoint: '/cognito/read',
        method: 'GET',
        description: 'Listar usuários do Cognito'
      },
      tokenOperations: {
        endpoint: '/cognito/token',
        method: 'POST',
        description: 'Operações com tokens'
      }
    }
  });
}

// Middleware CORS simplificado para debug
const simpleCorsHandler = async (event, context) => {
  console.log('🌐 CORS SIMPLES: Iniciando...');
  console.log('🌐 CORS SIMPLES: Origin =', event.headers?.Origin || event.headers?.origin);

  try {
    const result = await cognitoHandler(event, context);
    console.log('🌐 CORS SIMPLES: Handler executado, resultado:', {
      statusCode: result.statusCode,
      hasHeaders: !!result.headers
    });

    // Adiciona headers CORS básicos - origem específica para withCredentials
    const origin = event.headers?.Origin || event.headers?.origin;
    const corsHeaders = {
      'Access-Control-Allow-Origin': origin || 'https://dev.dsm.darede.com.br',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Origin, DynamoDB',
      'Access-Control-Allow-Credentials': 'true'
    };

    console.log('🌐 CORS SIMPLES: Headers CORS criados:', corsHeaders);

    const finalResult = {
      ...result,
      headers: {
        ...result.headers,
        ...corsHeaders  
      }
    };

    console.log('🌐 CORS SIMPLES: Resposta final:', {
      statusCode: finalResult.statusCode,
      headers: finalResult.headers
    });

    return finalResult;
  } catch (error) {
    console.error('🌐 CORS SIMPLES: Erro:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': 'https://dev.dsm.darede.com.br',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ error: 'Erro interno' })
    };
  }
};

exports.handler = simpleCorsHandler;
