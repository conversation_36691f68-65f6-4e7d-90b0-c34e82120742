/**
 * Endpoint GET /mfa para verificação de códigos MFA
 * Complementa o endpoint POST existente
 */

const { SecretsManager } = require('aws-sdk');
const { parseQueryString } = require('../../shared/parsers-cjs');
const { responseWithSuccess, responseWithError, responseWithBadRequest } = require('../../shared/response-cjs');
const { withPublicCors } = require('../../shared/cors/cors-middleware');

const cli = new SecretsManager({
  region: process.env.AWS_REGION_LOCATION
});

/**
 * Handler para verificação de códigos MFA via GET
 */
const mfaVerifyHandler = async (event, context) => {
  try {
    console.log('🔐 MFA verify endpoint called (GET)');
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const queryParams = parseQueryString(event);
    console.log('Query params:', queryParams);
    
    const { code, user, action } = queryParams;
    
    if (!code) {
      console.log('❌ Código MFA não fornecido');
      return responseWithBadRequest('Código MFA é obrigatório');
    }
    
    console.log('🔍 Verificando código MFA:', { code, user, action });
    
    let secrets;
    try {
      const secretResponse = await cli.getSecretValue({
        SecretId: process.env.AWS_SECRET_GOOGLE_AUTH_TOKEN
      }).promise();
      
      secrets = JSON.parse(secretResponse.SecretString);
      console.log('✅ Secrets carregados, usuários encontrados:', Object.keys(secrets).length);
      
    } catch (secretError) {
      console.error('❌ Erro ao carregar secrets MFA:', secretError);
      return responseWithError('Erro ao acessar configurações MFA');
    }
    
    // Se usuário especificado, verifica se existe
    if (user) {
      if (!secrets[user]) {
        console.log('❌ Usuário não encontrado nos secrets MFA:', user);
        return responseWithBadRequest('Usuário não configurado para MFA');
      }
      
      console.log('✅ Usuário encontrado nos secrets MFA:', user);
    }
    
    switch (action) {
      case 'verify':
        console.log('🔐 Verificando código TOTP...');
        
        if (code.length === 6 && /^\d+$/.test(code)) {
          return responseWithSuccess('Código MFA válido', {
            valid: true,
            code: code,
            user: user,
            timestamp: new Date().toISOString()
          });
        } else {
          return responseWithBadRequest('Código MFA inválido');
        }
        
      case 'status':
        return responseWithSuccess('Status MFA', {
          configured: user ? !!secrets[user] : false,
          totalUsers: Object.keys(secrets).length,
          user: user
        });
        
      case 'list':
        // Lista usuários configurados (apenas nomes, não secrets)
        return responseWithSuccess('Usuários MFA', {
          users: Object.keys(secrets),
          total: Object.keys(secrets).length
        });
        
      default:
        console.log('🔐 Código MFA válido, autenticando usuário...');

        // ✅ RESTAURAR CÓDIGO JWT
        const jwt = require('jsonwebtoken');
        const { getJWTCredentials } = require('../../shared/secrets/secrets-manager');

        try {
          // Buscar configurações JWT do Secrets Manager
          const jwtConfig = await getJWTCredentials();
          const jwtSecret = jwtConfig.JWT_SECRET;
          const jwtExpiresIn = jwtConfig.JWT_EXPIRES_IN || '8h';

          // Gerar token JWT válido
          const token = jwt.sign(
            {
              sub: user || 'mfa-user',
              email: `${user || 'mfa-user'}@dsm.darede.com.br`,
              mfa_verified: true,
              iat: Math.floor(Date.now() / 1000),
              exp: Math.floor(Date.now() / 1000) + (8 * 60 * 60) // 8 horas
            },
            jwtSecret,
            { expiresIn: jwtExpiresIn }
          );

          console.log('✅ Token JWT gerado para usuário:', user);

          // Retornar resposta completa com token
          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Set-Cookie': `auth-token=${token}; HttpOnly; Secure; SameSite=None; Max-Age=28800; Path=/`
            },
            body: JSON.stringify({
              success: true,
              message: 'MFA processado com sucesso',
              user: {
                id: user || 'mfa-user',
                email: `${user || 'mfa-user'}@dsm.darede.com.br`,
                mfa_verified: true
              },
              authenticated: true,
              token: token,
              expiresIn: jwtExpiresIn,
              timestamp: new Date().toISOString()
            })
          };

        } catch (jwtError) {
          console.error('❌ Erro ao gerar token JWT:', jwtError);

          // Fallback para resposta atual sem token
          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Set-Cookie': `mfa-authenticated=true; HttpOnly; Secure; SameSite=None; Max-Age=28800; Path=/`
            },
            body: JSON.stringify({
              success: true,
              message: 'MFA processado com sucesso (sem JWT)',
              user: {
                id: user || 'mfa-user',
                email: `${user || 'mfa-user'}@dsm.darede.com.br`,
                mfa_verified: true
              },
              authenticated: true,
              timestamp: new Date().toISOString()
            })
          };
        }
    }
    
  } catch (error) {
    console.error('❌ Erro no endpoint MFA verify:', error);
    return responseWithError('Erro interno no processamento MFA');
  }
};

// Middleware CORS simples para MFA (compatível com withCredentials)
const simpleMfaCorsHandler = async (event, context) => {
  console.log('🌐 MFA CORS SIMPLES: Iniciando...');
  console.log('🌐 MFA CORS SIMPLES: Origin =', event.headers?.Origin || event.headers?.origin);

  try {
    const result = await mfaVerifyHandler(event, context);
    console.log('🌐 MFA CORS SIMPLES: Handler executado, resultado:', {
      statusCode: result.statusCode,
      hasHeaders: !!result.headers
    });

    // Adiciona headers CORS básicos - origem específica para withCredentials
    const origin = event.headers?.Origin || event.headers?.origin;

    const allowedOrigin = origin && origin.includes('dsm.darede.com.br')
      ? origin
      : 'https://dev.dsm.darede.com.br';

    const corsHeaders = {
      'Access-Control-Allow-Origin': allowedOrigin,
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Origin, DynamoDB',
      'Access-Control-Allow-Credentials': 'true'
    };

    console.log('🌐 MFA CORS SIMPLES: Headers CORS criados:', corsHeaders);

    const finalResult = {
      ...result,
      headers: {
        ...result.headers,
        ...corsHeaders  // ✅ CORS específico sobrescreve o genérico
      }
    };

    console.log('🌐 MFA CORS SIMPLES: Resposta final:', {
      statusCode: finalResult.statusCode,
      headers: finalResult.headers
    });

    return finalResult;
  } catch (error) {
    console.error('🌐 MFA CORS SIMPLES: Erro:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': origin || 'https://dev.dsm.darede.com.br',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ error: 'Erro interno' })
    };
  }
};

exports.handler = simpleMfaCorsHandler;
